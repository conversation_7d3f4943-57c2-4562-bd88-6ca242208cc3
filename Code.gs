/**
 * Google Apps Script für Rechnungsformatierung
 * Erstellt und formatiert eine Rechnung basierend auf der CSV-Vorlage
 */

function createInvoice() {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Sheet leeren
  sheet.clear();
  
  // Grundlegende Formatierung
  setupBasicFormatting(sheet);
  
  // Rechnungsdaten einfügen
  insertInvoiceData(sheet);
  
  // Zellen zusammenführen
  mergeCells(sheet);

  // Formatierung anwenden
  applyFormatting(sheet);
}

function mergeCells(sheet) {
  // Zeile 2: B2:D2 (Absenderadresse)
  sheet.getRange('B2:D2').merge();

  // Zeile 4: C4:D4 (Empfänger Name/Firma)
  sheet.getRange('C4:D4').merge();

  // Zeile 5: C5:D5 (Empfänger Straße/Hsnr)
  sheet.getRange('C5:D5').merge();

  // Zeile 6: C6:D6 (Empfänger PLZ/ORT)
  sheet.getRange('C6:D6').merge();

  // Zeile 8: C8:H8 (ORT/DATUM)
  sheet.getRange('C8:H8').merge();

  // Zeile 9: C9:H9 (Rechnung Nr.)
  sheet.getRange('C9:H9').merge();

  // Zeile 10: C10:H10 (Betreff)
  sheet.getRange('C10:H10').merge();

  // Zeile 11: C11:H11 (Anrede)
  sheet.getRange('C11:H11').merge();

  // Zeile 12: C12:H12 (Einleitungstext)
  sheet.getRange('C12:H12').merge();

  // Zeile 13: D13:F13 (Beschreibung) und G13:H13 (Betrag)
  sheet.getRange('D13:F13').merge();
  sheet.getRange('G13:H13').merge();

  // Zeile 14: D14:F14 (Platzhalter Beschreibung) und G14:H14 (Platzhalter Betrag)
  sheet.getRange('D14:F14').merge();
  sheet.getRange('G14:H14').merge();

  // Zeile 15: E15:F15 (leer) und G15:H15 (Gesamtsumme)
  sheet.getRange('E15:F15').merge();
  sheet.getRange('G15:H15').merge();

  // Zeile 16: C16:H16 (Rechtliche Hinweise)
  sheet.getRange('C16:H16').merge();

  // Zeile 17: C17:H17 (Überweisungshinweis)
  sheet.getRange('C17:H17').merge();

  // Zeile 18: C18:H18 (Grußformel)
  sheet.getRange('C18:H18').merge();

  // Zeile 19: C19:H19 (Unterschrift)
  sheet.getRange('C19:H19').merge();

  // Zeile 22: C22:D22 (Bankinstitut)
  sheet.getRange('C22:D22').merge();

  // Zeile 24: F24:G24 (Seite X von Y)
  sheet.getRange('F24:G24').merge();
}

function setupBasicFormatting(sheet) {
  // Spaltenbreiten setzen
  sheet.setColumnWidth(1, 50);   // A
  sheet.setColumnWidth(2, 150);  // B
  sheet.setColumnWidth(3, 200);  // C
  sheet.setColumnWidth(4, 150);  // D
  sheet.setColumnWidth(5, 100);  // E
  sheet.setColumnWidth(6, 100);  // F
  sheet.setColumnWidth(7, 150);  // G
  
  // Zeilenhöhen setzen
  sheet.setRowHeight(1, 30);
  sheet.setRowHeight(2, 25);
}

function insertInvoiceData(sheet) {
  // Absenderadresse (Zeile 2)
  sheet.getRange('B2').setValue('Andrea Amsel - Agrarfeld 1 - 45098 Altburg');
  
  // Empfängeradresse (Zeilen 3-7)
  sheet.getRange('G3').setValue('Andrea Amsel');
  sheet.getRange('C4').setValue('Empfänger Name/Firma');
  sheet.getRange('G4').setValue('Agrarfeld 1');
  sheet.getRange('C5').setValue('Empfänger Straße/Hsnr');
  sheet.getRange('G5').setValue('45098 Altburg');
  sheet.getRange('C6').setValue('Empfänger PLZ/ORT');
  sheet.getRange('F6').setValue('Tel.:');
  sheet.getRange('G6').setValue('34574 45 23 12');
  sheet.getRange('F7').setValue('E-Mail:');
  sheet.getRange('G7').setValue('<EMAIL>');
  
  // Rechnungsdetails (Zeilen 8-12)
  sheet.getRange('C8').setValue('ORT/DATUM');
  sheet.getRange('C9').setValue('Rechnung Nr. 2025-[RECHNUNGSNR]');
  sheet.getRange('C10').setValue('Betreff');
  sheet.getRange('C11').setValue('Anrede');
  sheet.getRange('C12').setValue('für die nachfolgend aufgeführten Leistungen/Gegenstände stelle ich Ihnen den folgenden Betrag in Rechnung:');
  
  // Tabellenkopf (Zeile 13)
  sheet.getRange('C13').setValue('Pos.');
  sheet.getRange('D13').setValue('Beschreibung');
  sheet.getRange('H13').setValue('Betrag');
  
  // Platzhalter für Tabelleninhalt (Zeile 14)
  sheet.getRange('C14').setValue('[INHALT DER TABELLE WIRD SPÄTER DYNAMISCH AUSGEFÜLLT!!]');
  
  // Gesamtsumme (Zeile 15)
  sheet.getRange('G15').setValue('Gesamtsumme');
  
  // Rechtliche Hinweise und Grußformel (Zeilen 16-19)
  sheet.getRange('C16').setValue('Gemäß § ....');
  sheet.getRange('C17').setValue('Bitte überweisen Sie ...');
  sheet.getRange('C18').setValue('Mit freundlichen Grüßen');
  sheet.getRange('C19').setValue('Andrea Amsel');
  
  // Bankdaten (Zeilen 22-24)
  sheet.getRange('C22').setValue('Bankinstitut');
  sheet.getRange('C23').setValue('IBAN:');
  sheet.getRange('D23').setValue('[IBAN]');
  sheet.getRange('C24').setValue('BIC:');
  sheet.getRange('D24').setValue('[BIC]');
  sheet.getRange('G24').setValue('Seite X von Y');
}

function applyFormatting(sheet) {
  // Absenderadresse formatieren
  const senderRange = sheet.getRange('B2');
  senderRange.setFontSize(10);
  senderRange.setFontWeight('normal');
  
  // Empfängeradresse formatieren
  const recipientRange = sheet.getRange('G3:G7');
  recipientRange.setFontWeight('bold');
  recipientRange.setVerticalAlignment('top');
  
  // Rechnungsnummer hervorheben
  const invoiceNumberRange = sheet.getRange('C9');
  invoiceNumberRange.setFontWeight('bold');
  invoiceNumberRange.setFontSize(12);
  
  // Tabellenkopf formatieren
  const headerRange = sheet.getRange('C13:H13');
  headerRange.setFontWeight('bold');
  headerRange.setBackground('#E8E8E8');
  headerRange.setBorder(true, true, true, true, true, true);
  
  // Gesamtsumme hervorheben
  const totalRange = sheet.getRange('G15:H15');
  totalRange.setFontWeight('bold');
  totalRange.setBorder(true, true, true, true, false, false);
  
  // Unterschrift formatieren
  const signatureRange = sheet.getRange('C19');
  signatureRange.setFontWeight('bold');
  
  // Bankdaten formatieren
  const bankRange = sheet.getRange('C22:D24');
  bankRange.setFontSize(10);
  
  // Seitenzahl formatieren
  const pageRange = sheet.getRange('G24');
  pageRange.setFontSize(10);
  pageRange.setHorizontalAlignment('right');
}

/**
 * Funktion zum Hinzufügen von Rechnungspositionen
 * @param {Array} items - Array von Objekten mit {pos, description, amount}
 */
function addInvoiceItems(items) {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  // Platzhalter-Zeile löschen
  sheet.deleteRow(14);
  
  let currentRow = 14;
  let total = 0;
  
  // Rechnungspositionen einfügen
  items.forEach((item, index) => {
    sheet.getRange(currentRow, 3).setValue(item.pos || (index + 1));
    sheet.getRange(currentRow, 4).setValue(item.description);
    sheet.getRange(currentRow, 8).setValue(item.amount);

    // Zellen für diese Zeile zusammenführen
    sheet.getRange(currentRow, 4, 1, 3).merge(); // D:F für Beschreibung
    sheet.getRange(currentRow, 7, 1, 2).merge(); // G:H für Betrag

    // Formatierung für Rechnungsposition
    const itemRange = sheet.getRange(currentRow, 3, 1, 6);
    itemRange.setBorder(true, true, true, true, false, true);

    total += parseFloat(item.amount) || 0;
    currentRow++;
  });
  
  // Gesamtsumme aktualisieren
  const totalRow = currentRow;
  sheet.getRange(totalRow, 7).setValue('Gesamtsumme');
  sheet.getRange(totalRow, 8).setValue(total.toFixed(2) + ' €');
  
  // Gesamtsumme formatieren
  const totalRange = sheet.getRange(totalRow, 7, 1, 2);
  totalRange.setFontWeight('bold');
  totalRange.setBorder(true, true, true, true, false, false);
  totalRange.setBackground('#F0F0F0');
}

/**
 * Beispielfunktion zum Testen mit Beispieldaten
 */
function testInvoiceWithSampleData() {
  createInvoice();
  
  const sampleItems = [
    {pos: 1, description: 'Beratungsleistung', amount: 150.00},
    {pos: 2, description: 'Materialkosten', amount: 75.50},
    {pos: 3, description: 'Fahrtkosten', amount: 25.00}
  ];
  
  addInvoiceItems(sampleItems);
  
  // Platzhalter ersetzen
  replacePlaceholders({
    rechnungsnr: '001',
    iban: 'DE89 3704 0044 0532 0130 00',
    bic: 'COBADEFFXXX'
  });
}

/**
 * Platzhalter in der Rechnung ersetzen
 * @param {Object} data - Objekt mit den Ersetzungsdaten
 */
function replacePlaceholders(data) {
  const sheet = SpreadsheetApp.getActiveSheet();
  
  if (data.rechnungsnr) {
    const invoiceCell = sheet.getRange('C9');
    invoiceCell.setValue(invoiceCell.getValue().replace('[RECHNUNGSNR]', data.rechnungsnr));
  }
  
  if (data.iban) {
    sheet.getRange('D23').setValue(data.iban);
  }
  
  if (data.bic) {
    sheet.getRange('D24').setValue(data.bic);
  }
}
