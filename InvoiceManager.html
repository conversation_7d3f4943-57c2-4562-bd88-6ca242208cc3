<!DOCTYPE html>
<html>
<head>
    <base target="_top">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 10px;
            background-color: #f9f9f9;
        }
        
        .section {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .section h3 {
            margin-top: 0;
            color: #1a73e8;
            border-bottom: 2px solid #e8f0fe;
            padding-bottom: 8px;
        }
        
        select, input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #1a73e8;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin: 5px 0;
            font-size: 14px;
        }
        
        button:hover {
            background-color: #1557b0;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .positions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .positions-table th, .positions-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .positions-table th {
            background-color: #f2f2f2;
        }
        
        .add-position {
            background-color: #28a745;
            font-size: 12px;
            padding: 5px 10px;
        }
        
        .add-position:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="section">
        <h3>📋 Existierende Rechnungen</h3>
        <select id="existingInvoices">
            <option value="">Rechnung auswählen...</option>
        </select>
        <button onclick="loadSelectedInvoice()">Rechnung laden</button>
        <button onclick="createSelectedInvoice()" style="background-color: #28a745; margin-top: 5px;">Rechnung erstellen</button>
        <small style="color: #666; display: block; margin-top: 5px;">
            Erstellt automatisch eine Rechnung mit Daten aus den Tabellenblättern "Daten" und "Artikel"
        </small>
    </div>

    <div class="section">
        <h3>➕ Neue Rechnung erstellen</h3>
        
        <div class="form-group">
            <label for="rechnungsnr">Rechnungsnummer:</label>
            <input type="text" id="rechnungsnr" placeholder="z.B. 001">
        </div>
        
        <div class="form-group">
            <label for="datum">Datum:</label>
            <input type="date" id="datum">
        </div>
        
        <div class="form-group">
            <label for="empfaengerName">Empfänger Name/Firma:</label>
            <input type="text" id="empfaengerName" placeholder="Firmenname oder Name">
        </div>
        
        <div class="form-group">
            <label for="empfaengerStrasse">Straße/Hausnummer:</label>
            <input type="text" id="empfaengerStrasse" placeholder="Musterstraße 123">
        </div>
        
        <div class="form-group">
            <label for="empfaengerPlzOrt">PLZ/Ort:</label>
            <input type="text" id="empfaengerPlzOrt" placeholder="12345 Musterstadt">
        </div>
        
        <div class="form-group">
            <label for="betreff">Betreff:</label>
            <input type="text" id="betreff" placeholder="Rechnung für...">
        </div>
        
        <div class="form-group">
            <label for="anrede">Anrede:</label>
            <textarea id="anrede" rows="2" placeholder="Sehr geehrte Damen und Herren,"></textarea>
        </div>
        
        <div class="form-group">
            <label>Rechnungspositionen:</label>
            <table class="positions-table" id="positionsTable">
                <thead>
                    <tr>
                        <th>Pos.</th>
                        <th>Beschreibung</th>
                        <th>Betrag (€)</th>
                    </tr>
                </thead>
                <tbody id="positionsBody">
                    <tr>
                        <td>1</td>
                        <td><input type="text" placeholder="Beschreibung" style="width:100%; border:none;"></td>
                        <td><input type="number" step="0.01" placeholder="0.00" style="width:100%; border:none;"></td>
                    </tr>
                </tbody>
            </table>
            <button class="add-position" onclick="addPosition()">+ Position hinzufügen</button>
        </div>
        
        <div class="form-group">
            <label for="iban">IBAN (optional):</label>
            <input type="text" id="iban" placeholder="DE89 3704 0044 0532 0130 00">
        </div>
        
        <div class="form-group">
            <label for="bic">BIC (optional):</label>
            <input type="text" id="bic" placeholder="COBADEFFXXX">
        </div>
        
        <button onclick="createNewInvoice()">Rechnung erstellen</button>
    </div>

    <div id="message"></div>

    <script>
        // Beim Laden der Sidebar existierende Rechnungen laden
        google.script.run
            .withSuccessHandler(populateExistingInvoices)
            .getExistingInvoiceNumbers();
        
        // Heutiges Datum als Standard setzen
        document.getElementById('datum').value = new Date().toISOString().split('T')[0];
        
        function populateExistingInvoices(invoiceNumbers) {
            const select = document.getElementById('existingInvoices');
            select.innerHTML = '<option value="">Rechnung auswählen...</option>';
            
            invoiceNumbers.forEach(number => {
                const option = document.createElement('option');
                option.value = number;
                option.textContent = `Rechnung ${number}`;
                select.appendChild(option);
            });
        }
        
        function loadSelectedInvoice() {
            const selectedInvoice = document.getElementById('existingInvoices').value;
            if (!selectedInvoice) {
                showMessage('Bitte wählen Sie eine Rechnung aus.', 'error');
                return;
            }

            google.script.run
                .withSuccessHandler(showMessage)
                .loadInvoice(selectedInvoice);
        }

        function createSelectedInvoice() {
            const selectedInvoice = document.getElementById('existingInvoices').value;
            if (!selectedInvoice) {
                showMessage('Bitte wählen Sie eine Rechnung aus.', 'error');
                return;
            }

            google.script.run
                .withSuccessHandler(function(result) {
                    showMessage(result, 'success');
                })
                .withFailureHandler(function(error) {
                    showMessage('Fehler beim Erstellen der Rechnung: ' + error.message, 'error');
                })
                .createInvoiceFromData(selectedInvoice);
        }
        
        function addPosition() {
            const tbody = document.getElementById('positionsBody');
            const rowCount = tbody.rows.length + 1;
            
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>${rowCount}</td>
                <td><input type="text" placeholder="Beschreibung" style="width:100%; border:none;"></td>
                <td><input type="number" step="0.01" placeholder="0.00" style="width:100%; border:none;"></td>
            `;
        }
        
        function createNewInvoice() {
            const rechnungsnr = document.getElementById('rechnungsnr').value;
            if (!rechnungsnr) {
                showMessage('Bitte geben Sie eine Rechnungsnummer ein.', 'error');
                return;
            }
            
            // Rechnungspositionen sammeln
            const positions = [];
            const rows = document.getElementById('positionsBody').rows;
            
            for (let i = 0; i < rows.length; i++) {
                const inputs = rows[i].querySelectorAll('input');
                const description = inputs[0].value.trim();
                const amount = parseFloat(inputs[1].value) || 0;
                
                if (description && amount > 0) {
                    positions.push({
                        pos: i + 1,
                        description: description,
                        amount: amount
                    });
                }
            }
            
            const invoiceData = {
                rechnungsnr: rechnungsnr,
                datum: document.getElementById('datum').value,
                empfaenger: {
                    name: document.getElementById('empfaengerName').value,
                    strasse: document.getElementById('empfaengerStrasse').value,
                    plzOrt: document.getElementById('empfaengerPlzOrt').value
                },
                betreff: document.getElementById('betreff').value,
                anrede: document.getElementById('anrede').value,
                positionen: positions,
                iban: document.getElementById('iban').value,
                bic: document.getElementById('bic').value
            };
            
            google.script.run
                .withSuccessHandler(function(result) {
                    showMessage(result, 'success');
                    // Formular zurücksetzen
                    document.getElementById('rechnungsnr').value = '';
                    // Existierende Rechnungen neu laden
                    google.script.run
                        .withSuccessHandler(populateExistingInvoices)
                        .getExistingInvoiceNumbers();
                })
                .withFailureHandler(function(error) {
                    showMessage('Fehler beim Erstellen der Rechnung: ' + error.message, 'error');
                })
                .createNewInvoice(invoiceData);
        }
        
        function showMessage(message, type = 'success') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            
            // Nachricht nach 5 Sekunden ausblenden
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 5000);
        }
    </script>
</body>
</html>
