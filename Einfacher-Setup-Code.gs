/**
 * Einfache Rechnungsvorlage Setup
 * Erstellt automatisch eine DIN-konforme Rechnungsvorlage
 */

function erstelleRechnungsvorlage() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet();
  
  // Rechnungsblatt komplett löschen falls vorhanden
  let rechnungSheet = sheet.getSheetByName('Rechnung');
  if (rechnungSheet) {
    sheet.deleteSheet(rechnungSheet);
  }
  
  // Neues Rechnungsblatt erstellen
  rechnungSheet = sheet.insertSheet('Rechnung');
  
  // Rechnungsblatt einrichten
  setupRechnungsblatt(rechnungSheet);
  
  SpreadsheetApp.getUi().alert('Rechnungsvorlage erfolgreich erstellt!');
}

function setupRechnungsblatt(sheet) {
  // Alle Zellen löschen
  sheet.clear();
  
  // Spaltenbreiten für DIN A4 optimiert
  sheet.setColumnWidth(1, 76);   // 2cm links
  sheet.setColumnWidth(2, 19);   // 0,5cm Abstand
  sheet.setColumnWidth(3, 200);  // Absender (breit genug)
  sheet.setColumnWidth(4, 200);  // Empfänger
  sheet.setColumnWidth(5, 100);  // Rest
  
  // Zellenverbindungen VOR dem Setzen der Inhalte
  sheet.getRange('B2:E2').merge();  // Zeile 2: BCDE - Absender
  sheet.getRange('C4:E4').merge();  // Zeile 4: CDE - Empfänger Name/Firma
  sheet.getRange('C5:E5').merge();  // Zeile 5: CDE - Empfänger Straße/Hsnr
  sheet.getRange('C6:E6').merge();  // Zeile 6: CDE - Empfänger PLZ/ORT
  sheet.getRange('G7:H7').merge();  // Zeile 7: GH - E-Mail
  sheet.getRange('G8:H8').merge();  // Zeile 8: GH - ORT/DATUM
  sheet.getRange('C9:H9').merge();  // Zeile 9: CDEFGH - Rechnungsnummer
  sheet.getRange('C10:H10').merge(); // Zeile 10: CDEFGH - Betreff
  sheet.getRange('C11:H11').merge(); // Zeile 11: CDEFGH - Anrede
  sheet.getRange('C12:H12').merge(); // Zeile 12: CDEFGH - Einleitungstext
  sheet.getRange('D13:G13').merge(); // Zeile 13: DEFG - Beschreibung
  sheet.getRange('H13:I13').merge(); // Zeile 13: HI - Betrag
  sheet.getRange('C14:H14').merge(); // Zeile 14: CDEFGH - Tabelleninhalt
  sheet.getRange('G15:H15').merge(); // Zeile 15: GH - Gesamtsumme
  sheet.getRange('C16:H16').merge(); // Zeile 16: CDEFGH - UStG Hinweis
  sheet.getRange('C17:H17').merge(); // Zeile 17: CDEFGH - Zahlungshinweis
  sheet.getRange('C18:H18').merge(); // Zeile 18: CDEFGH - Grußformel
  sheet.getRange('C19:H19').merge(); // Zeile 19: CDEFGH - Unterschrift
  sheet.getRange('C21:D21').merge(); // Zeile 21: CD - Bankinstitut
  sheet.getRange('D22:E22').merge(); // Zeile 22: DE - IBAN
  sheet.getRange('D23:E23').merge(); // Zeile 23: DE - BIC
  sheet.getRange('G23:H23').merge(); // Zeile 23: GH - Seitenzahl
  
  // Jetzt Inhalte setzen (exakt wie in der CSV)
  sheet.getRange('B2').setValue('Andrea Amsel - Agrarfeld 1 - 45098 Altburg');
  sheet.getRange('G3').setValue('Andrea Amsel');
  sheet.getRange('C4').setValue('Empfänger Name/Firma');
  sheet.getRange('G4').setValue('Agrarfeld 1');
  sheet.getRange('C5').setValue('Empfänger Straße/Hsnr');
  sheet.getRange('G5').setValue('45098 Altburg');
  sheet.getRange('C6').setValue('Empfänger PLZ/ORT');
  sheet.getRange('F6').setValue('Tel.:');
  sheet.getRange('G6').setValue('34574 45 23 12');
  sheet.getRange('F7').setValue('E-Mail:');
  sheet.getRange('G7').setValue('<EMAIL>');
  sheet.getRange('G8').setValue('ORT/DATUM');
  sheet.getRange('C9').setValue('Rechnung Nr. 2025-[RECHNUNGSNR]');
  sheet.getRange('C10').setValue('Betreff');
  sheet.getRange('C11').setValue('Anrede');
  sheet.getRange('C12').setValue('für die nachfolgend aufgeführten Leistungen/Gegenstände stelle ich Ihnen den folgenden Betrag in Rechnung:');
  sheet.getRange('C13').setValue('Pos.');
  sheet.getRange('D13').setValue('Beschreibung');
  sheet.getRange('H13').setValue('Betrag');
  sheet.getRange('C14').setValue('[INHALT DER TABELLE WIRD SPÄTER DYNAMISCH AUSGEFÜLLT!!]');
  sheet.getRange('G15').setValue('Gesamtsumme');
  sheet.getRange('C16').setValue('Gemäß § ....');
  sheet.getRange('C17').setValue('Bitte überweisen Sie ...');
  sheet.getRange('C18').setValue('Mit freundlichen Grüßen');
  sheet.getRange('C19').setValue('Andrea Amsel');
  sheet.getRange('C21').setValue('Bankinstitut');
  sheet.getRange('C22').setValue('IBAN:');
  sheet.getRange('D22').setValue('[IBAN]');
  sheet.getRange('C23').setValue('BIC:');
  sheet.getRange('D23').setValue('[BIC]');
  sheet.getRange('G23').setValue('Seite X von Y');
  
  // Formatierung anwenden
  formatRechnungsblatt(sheet);
}

function formatRechnungsblatt(sheet) {
  // Absender B2: Arial, 8pt, vertikal unten, horizontal zentriert, Rahmenlinie NUR UNTEN
  sheet.getRange('B2').setFontFamily('Arial')
                      .setFontSize(8)
                      .setVerticalAlignment('bottom')
                      .setHorizontalAlignment('center')
                      .setBorder(false, false, false, false, true, false);
  
  // Empfänger C4-C6: Arial, 10,5pt, linksbündig
  sheet.getRange('C4:C6').setFontFamily('Arial')
                         .setFontSize(10.5)
                         .setHorizontalAlignment('left')
                         .setVerticalAlignment('top');
  
  // Kontaktdaten rechts: Arial, 10,5pt
  sheet.getRange('G3:G6').setFontFamily('Arial').setFontSize(10.5);
  sheet.getRange('F6:G7').setFontFamily('Arial').setFontSize(10.5);
  
  // Ort/Datum: Arial, 10,5pt
  sheet.getRange('G8').setFontFamily('Arial').setFontSize(10.5);
  
  // Rechnungsnummer: Arial, 10,5pt
  sheet.getRange('C9').setFontFamily('Arial').setFontSize(10.5);
  
  // Betreff: Arial, 10,5pt
  sheet.getRange('C10').setFontFamily('Arial').setFontSize(10.5);
  
  // Anrede: Arial, 10,5pt
  sheet.getRange('C11').setFontFamily('Arial').setFontSize(10.5);
  
  // Einleitungstext: Arial, 10,5pt
  sheet.getRange('C12').setFontFamily('Arial').setFontSize(10.5);
  
  // Tabellenkopf: Arial, 10,5pt, fett, Rahmen
  sheet.getRange('C13:G13').setFontFamily('Arial')
                           .setFontSize(10.5)
                           .setFontWeight('bold')
                           .setBorder(true, true, true, true, true, true);
  
  // Tabelleninhalt: Arial, 10,5pt
  sheet.getRange('C14').setFontFamily('Arial').setFontSize(10.5);
  
  // Gesamtsumme: Arial, 10,5pt, fett
  sheet.getRange('G15').setFontFamily('Arial').setFontSize(10.5).setFontWeight('bold');
  
  // UStG Hinweis: Arial, 10,5pt
  sheet.getRange('C16').setFontFamily('Arial').setFontSize(10.5);
  
  // Zahlungshinweis: Arial, 10,5pt
  sheet.getRange('C17').setFontFamily('Arial').setFontSize(10.5);
  
  // Grußformel: Arial, 10,5pt
  sheet.getRange('C18').setFontFamily('Arial').setFontSize(10.5);
  
  // Unterschrift: Arial, 10,5pt
  sheet.getRange('C19').setFontFamily('Arial').setFontSize(10.5);
  
  // Bankdaten: Arial, 10,5pt
  sheet.getRange('C21:C23').setFontFamily('Arial').setFontSize(10.5);
  sheet.getRange('D22:D23').setFontFamily('Arial').setFontSize(10.5);
  
  // Seitenzahl: Arial, 10,5pt
  sheet.getRange('G23').setFontFamily('Arial').setFontSize(10.5);
  
  // Zahlenformat für Preise
  sheet.getRange('E:E').setNumberFormat('#,##0.00 €');
  
  // Zeilenhöhen (DIN 5008: 12pt Zeilenabstand = 16px)
  sheet.setRowHeight(2, 16);   // Absender
  sheet.setRowHeight(3, 16);   // Kontaktdaten rechts
  sheet.setRowHeight(4, 16);   // Empfänger Name/Firma
  sheet.setRowHeight(5, 16);   // Empfänger Straße/Hsnr
  sheet.setRowHeight(6, 16);   // Empfänger PLZ/ORT + Tel.
  sheet.setRowHeight(7, 16);   // E-Mail
  sheet.setRowHeight(8, 16);   // ORT/DATUM
  sheet.setRowHeight(9, 16);   // Rechnungsnummer
  sheet.setRowHeight(10, 16);  // Betreff
  sheet.setRowHeight(11, 16);  // Anrede
  sheet.setRowHeight(12, 16);  // Einleitungstext
  sheet.setRowHeight(13, 18);  // Tabellenkopf
  sheet.setRowHeight(14, 16);  // Tabelleninhalt
  sheet.setRowHeight(15, 18);  // Gesamtsumme
  sheet.setRowHeight(16, 16);  // UStG Hinweis
  sheet.setRowHeight(17, 16);  // Zahlungshinweis
  sheet.setRowHeight(18, 16);  // Grußformel
  sheet.setRowHeight(19, 16);  // Unterschrift
  sheet.setRowHeight(21, 16);  // Bankinstitut
  sheet.setRowHeight(22, 16);  // IBAN
  sheet.setRowHeight(23, 16);  // BIC + Seitenzahl
}





/**
 * Menü erstellen
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Rechnungsvorlage')
    .addItem('Vorlage erstellen', 'erstelleRechnungsvorlage')
    .addToUi();
}
