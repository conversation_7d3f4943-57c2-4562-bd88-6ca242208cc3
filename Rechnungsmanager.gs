/**
 * AKTIVER RECHNUNGSMANAGER
 * Für die laufende Verwaltung und Erstellung von Rechnungen
 * Sidebar mit Dropdown und Formular für neue Rechnungen
 */

/**
 * Menü in Google Sheets hinzufügen
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Rechnungen')
    .addItem('Rechnung verwalten', 'showInvoiceManager')
    .addToUi();
}

/**
 * Rechnungsmanager-Sidebar anzeigen
 */
function showInvoiceManager() {
  const html = HtmlService.createHtmlOutputFromFile('InvoiceManager')
    .setWidth(350)
    .setTitle('Rechnungsmanager');
  SpreadsheetApp.getUi().showSidebar(html);
}

/**
 * Alle existierenden Rechnungsnummern abrufen
 */
function getExistingInvoiceNumbers() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sheets = spreadsheet.getSheets();
  const invoiceNumbers = [];
  
  sheets.forEach(sheet => {
    const name = sheet.getName();
    // Prüfen ob Sheet-Name eine Rechnungsnummer ist (Format: Rechnung-XXX)
    if (name.startsWith('Rechnung-')) {
      const number = name.replace('Rechnung-', '');
      invoiceNumbers.push(number);
    }
  });
  
  return invoiceNumbers.sort();
}

/**
 * Neue Rechnung erstellen
 */
function createNewInvoice(invoiceData) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let templateSheet = spreadsheet.getSheetByName('Rechnung');
  
  // Falls das Template-Sheet "Rechnung" nicht existiert, Fehler werfen
  if (!templateSheet) {
    throw new Error('Vorlage "Rechnung" nicht gefunden! Bitte erstellen Sie zuerst die Vorlage mit dem Setup-Code.');
  }
  
  // Prüfen ob Rechnung bereits existiert
  const existingSheet = spreadsheet.getSheetByName(`Rechnung-${invoiceData.rechnungsnr}`);
  if (existingSheet) {
    throw new Error(`Rechnung ${invoiceData.rechnungsnr} existiert bereits!`);
  }
  
  // Neues Sheet für die Rechnung erstellen
  const newSheet = spreadsheet.insertSheet(`Rechnung-${invoiceData.rechnungsnr}`);
  
  // Vorlage kopieren
  templateSheet.getRange('A1:I30').copyTo(newSheet.getRange('A1:I30'));
  
  // Rechnungsdaten einfügen
  if (invoiceData.empfaenger) {
    if (invoiceData.empfaenger.name) {
      newSheet.getRange('C4').setValue(invoiceData.empfaenger.name);
    }
    if (invoiceData.empfaenger.strasse) {
      newSheet.getRange('C5').setValue(invoiceData.empfaenger.strasse);
    }
    if (invoiceData.empfaenger.plzOrt) {
      newSheet.getRange('C6').setValue(invoiceData.empfaenger.plzOrt);
    }
  }
  
  if (invoiceData.datum) {
    newSheet.getRange('C8').setValue(invoiceData.datum);
  }
  
  if (invoiceData.betreff) {
    newSheet.getRange('C10').setValue(invoiceData.betreff);
  }
  
  if (invoiceData.anrede) {
    newSheet.getRange('C11').setValue(invoiceData.anrede);
  }
  
  // Rechnungsnummer ersetzen
  const invoiceCell = newSheet.getRange('C9');
  invoiceCell.setValue(invoiceCell.getValue().replace('[RECHNUNGSNR]', invoiceData.rechnungsnr));
  
  // IBAN und BIC ersetzen
  if (invoiceData.iban) {
    newSheet.getRange('D23').setValue(invoiceData.iban);
  }
  if (invoiceData.bic) {
    newSheet.getRange('D24').setValue(invoiceData.bic);
  }
  
  // Rechnungspositionen hinzufügen
  if (invoiceData.positionen && invoiceData.positionen.length > 0) {
    addInvoiceItems(invoiceData.positionen, newSheet);
  }
  
  // Zum neuen Sheet wechseln
  spreadsheet.setActiveSheet(newSheet);
  
  return `Rechnung ${invoiceData.rechnungsnr} wurde erfolgreich erstellt!`;
}

/**
 * Rechnungspositionen hinzufügen
 */
function addInvoiceItems(items, targetSheet) {
  const sheet = targetSheet;
  
  // Platzhalter-Zeile löschen
  sheet.deleteRow(14);
  
  let currentRow = 14;
  let total = 0;
  
  // Rechnungspositionen einfügen
  items.forEach((item, index) => {
    sheet.getRange(currentRow, 3).setValue(item.pos || (index + 1));
    sheet.getRange(currentRow, 4).setValue(item.description);
    sheet.getRange(currentRow, 8).setValue(item.amount);
    
    // Zellen für diese Zeile zusammenführen
    sheet.getRange(currentRow, 4, 1, 3).merge(); // D:F für Beschreibung
    sheet.getRange(currentRow, 7, 1, 2).merge(); // G:H für Betrag
    
    // Formatierung für Rechnungsposition
    const itemRange = sheet.getRange(currentRow, 3, 1, 6);
    itemRange.setBorder(true, true, true, true, false, true);
    
    // Rechtsbündige Ausrichtung für Betrag
    sheet.getRange(currentRow, 7, 1, 2).setHorizontalAlignment('right');
    
    total += parseFloat(item.amount) || 0;
    currentRow++;
  });
  
  // Gesamtsumme aktualisieren
  const totalRow = currentRow;
  sheet.getRange(totalRow, 5).setValue('Gesamtsumme'); // E-Spalte
  sheet.getRange(totalRow, 7).setValue(total.toFixed(2) + ' €'); // G-Spalte
  
  // Zellen für Gesamtsumme zusammenführen
  sheet.getRange(totalRow, 5, 1, 2).merge(); // E:F für "Gesamtsumme"
  sheet.getRange(totalRow, 7, 1, 2).merge(); // G:H für Betrag
  
  // Gesamtsumme formatieren
  const totalRange = sheet.getRange(totalRow, 5, 1, 4); // E bis H
  totalRange.setFontWeight('bold');
  totalRange.setBorder(true, true, true, true, false, false);
  totalRange.setBackground('#F0F0F0');
  
  // Rechtsbündige Ausrichtung für Gesamtsumme
  sheet.getRange(totalRow, 5, 1, 2).setHorizontalAlignment('right'); // E:F
  sheet.getRange(totalRow, 7, 1, 2).setHorizontalAlignment('right'); // G:H
}

/**
 * Rechnung laden
 */
function loadInvoice(invoiceNumber) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sheetName = `Rechnung-${invoiceNumber}`;
  const sheet = spreadsheet.getSheetByName(sheetName);
  
  if (sheet) {
    spreadsheet.setActiveSheet(sheet);
    return `Rechnung ${invoiceNumber} wurde geladen.`;
  } else {
    return `Rechnung ${invoiceNumber} wurde nicht gefunden.`;
  }
}
