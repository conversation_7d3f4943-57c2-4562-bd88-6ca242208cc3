/**
 * AKTIVER RECHNUNGSMANAGER
 * Für die laufende Verwaltung und Erstellung von Rechnungen
 * Sidebar mit Dropdown und Formular für neue Rechnungen
 */

/**
 * Menü in Google Sheets hinzufügen
 */
function onOpen() {
  const ui = SpreadsheetApp.getUi();
  ui.createMenu('Rechnungen')
    .addItem('Rechnung verwalten', 'showInvoiceManager')
    .addToUi();
}

/**
 * Rechnungsmanager-Sidebar anzeigen
 */
function showInvoiceManager() {
  const html = HtmlService.createHtmlOutputFromFile('InvoiceManager')
    .setWidth(350)
    .setTitle('Rechnungsmanager');
  SpreadsheetApp.getUi().showSidebar(html);
}

/**
 * Alle existierenden Rechnungsnummern abrufen (aus Daten-Tabellenblatt)
 */
function getExistingInvoiceNumbers() {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const datenSheet = spreadsheet.getSheetByName('Daten');

  if (!datenSheet) {
    console.log('Tabellenblatt "Daten" nicht gefunden');
    return [];
  }

  const data = datenSheet.getDataRange().getValues();
  const invoiceNumbers = [];

  // Erste Zeile überspringen (Header)
  for (let i = 1; i < data.length; i++) {
    const rechnungsnr = data[i][0]; // Spalte A: RECHNUNGSNR
    if (rechnungsnr && !invoiceNumbers.includes(rechnungsnr)) {
      invoiceNumbers.push(rechnungsnr.toString());
    }
  }

  return invoiceNumbers.sort();
}

/**
 * Kundendaten für eine Rechnungsnummer abrufen
 */
function getCustomerData(rechnungsnr) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const datenSheet = spreadsheet.getSheetByName('Daten');

  if (!datenSheet) {
    throw new Error('Tabellenblatt "Daten" nicht gefunden');
  }

  const data = datenSheet.getDataRange().getValues();

  // Nach Rechnungsnummer suchen
  for (let i = 1; i < data.length; i++) {
    if (data[i][0] == rechnungsnr) {
      return {
        rechnungsnr: data[i][0],
        anrede: data[i][1],
        namefirma: data[i][2],
        strasse: data[i][3],
        plzort: data[i][4],
        dlgegenstand: data[i][5]
      };
    }
  }

  return null;
}

/**
 * Artikel/Positionen für eine Rechnungsnummer abrufen
 */
function getInvoiceItems(rechnungsnr) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const artikelSheet = spreadsheet.getSheetByName('Artikel');

  if (!artikelSheet) {
    throw new Error('Tabellenblatt "Artikel" nicht gefunden');
  }

  const data = artikelSheet.getDataRange().getValues();
  const items = [];

  // Nach Rechnungsnummer suchen
  for (let i = 1; i < data.length; i++) {
    if (data[i][0] == rechnungsnr) {
      const preis = data[i][2];
      // Preis von "10,00 €" zu 10.00 konvertieren
      const cleanPrice = typeof preis === 'string' ?
        parseFloat(preis.replace('€', '').replace(',', '.').trim()) :
        parseFloat(preis);

      items.push({
        pos: items.length + 1,
        description: data[i][1],
        amount: cleanPrice || 0
      });
    }
  }

  return items;
}

/**
 * Neue Rechnung aus Daten erstellen (basierend auf Rechnungsnummer)
 */
function createInvoiceFromData(rechnungsnr) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let templateSheet = spreadsheet.getSheetByName('Rechnung');

  // Falls das Template-Sheet "Rechnung" nicht existiert, Fehler werfen
  if (!templateSheet) {
    throw new Error('Vorlage "Rechnung" nicht gefunden! Bitte erstellen Sie zuerst die Vorlage mit dem Setup-Code.');
  }

  // Prüfen ob Rechnung bereits existiert
  const existingSheet = spreadsheet.getSheetByName(`Rechnung-${rechnungsnr}`);
  if (existingSheet) {
    throw new Error(`Rechnung ${rechnungsnr} existiert bereits!`);
  }

  // Kundendaten aus "Daten"-Tabellenblatt laden
  const customerData = getCustomerData(rechnungsnr);
  if (!customerData) {
    throw new Error(`Keine Kundendaten für Rechnung ${rechnungsnr} gefunden!`);
  }

  // Artikel aus "Artikel"-Tabellenblatt laden
  const items = getInvoiceItems(rechnungsnr);
  if (items.length === 0) {
    throw new Error(`Keine Artikel für Rechnung ${rechnungsnr} gefunden!`);
  }

  // Neues Sheet für die Rechnung erstellen
  const newSheet = spreadsheet.insertSheet(`Rechnung-${rechnungsnr}`);

  // Vorlage kopieren
  templateSheet.getRange('A1:I30').copyTo(newSheet.getRange('A1:I30'));

  // Kundendaten einfügen
  newSheet.getRange('C4').setValue(customerData.namefirma);
  newSheet.getRange('C5').setValue(customerData.strasse);
  newSheet.getRange('C6').setValue(customerData.plzort);

  // Heutiges Datum einfügen
  const today = new Date();
  const dateString = today.toLocaleDateString('de-DE');
  newSheet.getRange('C8').setValue(dateString);

  // Betreff basierend auf DL/GEG
  const betreff = customerData.dlgegenstand === 'DL' ?
    'Rechnung für erbrachte Dienstleistungen' :
    'Rechnung für gelieferte Gegenstände';
  newSheet.getRange('C10').setValue(betreff);

  // Anrede einfügen
  newSheet.getRange('C11').setValue(customerData.anrede);

  // Rechnungsnummer ersetzen
  const invoiceCell = newSheet.getRange('C9');
  invoiceCell.setValue(invoiceCell.getValue().replace('[RECHNUNGSNR]', rechnungsnr));

  // Rechnungspositionen hinzufügen
  addInvoiceItems(items, newSheet);

  // Zum neuen Sheet wechseln
  spreadsheet.setActiveSheet(newSheet);

  return `Rechnung ${rechnungsnr} wurde erfolgreich erstellt!`;
}

/**
 * Neue Rechnung erstellen (alte Funktion für manuelle Eingabe - optional)
 */
function createNewInvoice(invoiceData) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  let templateSheet = spreadsheet.getSheetByName('Rechnung');

  // Falls das Template-Sheet "Rechnung" nicht existiert, Fehler werfen
  if (!templateSheet) {
    throw new Error('Vorlage "Rechnung" nicht gefunden! Bitte erstellen Sie zuerst die Vorlage mit dem Setup-Code.');
  }

  // Prüfen ob Rechnung bereits existiert
  const existingSheet = spreadsheet.getSheetByName(`Rechnung-${invoiceData.rechnungsnr}`);
  if (existingSheet) {
    throw new Error(`Rechnung ${invoiceData.rechnungsnr} existiert bereits!`);
  }

  // Neues Sheet für die Rechnung erstellen
  const newSheet = spreadsheet.insertSheet(`Rechnung-${invoiceData.rechnungsnr}`);

  // Vorlage kopieren
  templateSheet.getRange('A1:I30').copyTo(newSheet.getRange('A1:I30'));

  // Rechnungsdaten einfügen
  if (invoiceData.empfaenger) {
    if (invoiceData.empfaenger.name) {
      newSheet.getRange('C4').setValue(invoiceData.empfaenger.name);
    }
    if (invoiceData.empfaenger.strasse) {
      newSheet.getRange('C5').setValue(invoiceData.empfaenger.strasse);
    }
    if (invoiceData.empfaenger.plzOrt) {
      newSheet.getRange('C6').setValue(invoiceData.empfaenger.plzOrt);
    }
  }

  if (invoiceData.datum) {
    newSheet.getRange('C8').setValue(invoiceData.datum);
  }

  if (invoiceData.betreff) {
    newSheet.getRange('C10').setValue(invoiceData.betreff);
  }

  if (invoiceData.anrede) {
    newSheet.getRange('C11').setValue(invoiceData.anrede);
  }

  // Rechnungsnummer ersetzen
  const invoiceCell = newSheet.getRange('C9');
  invoiceCell.setValue(invoiceCell.getValue().replace('[RECHNUNGSNR]', invoiceData.rechnungsnr));

  // IBAN und BIC ersetzen
  if (invoiceData.iban) {
    newSheet.getRange('D23').setValue(invoiceData.iban);
  }
  if (invoiceData.bic) {
    newSheet.getRange('D24').setValue(invoiceData.bic);
  }

  // Rechnungspositionen hinzufügen
  if (invoiceData.positionen && invoiceData.positionen.length > 0) {
    addInvoiceItems(invoiceData.positionen, newSheet);
  }

  // Zum neuen Sheet wechseln
  spreadsheet.setActiveSheet(newSheet);

  return `Rechnung ${invoiceData.rechnungsnr} wurde erfolgreich erstellt!`;
}

/**
 * Rechnungspositionen hinzufügen
 */
function addInvoiceItems(items, targetSheet) {
  const sheet = targetSheet;
  
  // Platzhalter-Zeile löschen
  sheet.deleteRow(14);
  
  let currentRow = 14;
  let total = 0;
  
  // Rechnungspositionen einfügen
  items.forEach((item, index) => {
    sheet.getRange(currentRow, 3).setValue(item.pos || (index + 1));
    sheet.getRange(currentRow, 4).setValue(item.description);
    sheet.getRange(currentRow, 8).setValue(item.amount);
    
    // Zellen für diese Zeile zusammenführen
    sheet.getRange(currentRow, 4, 1, 3).merge(); // D:F für Beschreibung
    sheet.getRange(currentRow, 7, 1, 2).merge(); // G:H für Betrag
    
    // Formatierung für Rechnungsposition
    const itemRange = sheet.getRange(currentRow, 3, 1, 6);
    itemRange.setBorder(true, true, true, true, false, true);
    
    // Rechtsbündige Ausrichtung für Betrag
    sheet.getRange(currentRow, 7, 1, 2).setHorizontalAlignment('right');
    
    total += parseFloat(item.amount) || 0;
    currentRow++;
  });
  
  // Gesamtsumme aktualisieren
  const totalRow = currentRow;
  sheet.getRange(totalRow, 5).setValue('Gesamtsumme'); // E-Spalte
  sheet.getRange(totalRow, 7).setValue(total.toFixed(2) + ' €'); // G-Spalte
  
  // Zellen für Gesamtsumme zusammenführen
  sheet.getRange(totalRow, 5, 1, 2).merge(); // E:F für "Gesamtsumme"
  sheet.getRange(totalRow, 7, 1, 2).merge(); // G:H für Betrag
  
  // Gesamtsumme formatieren
  const totalRange = sheet.getRange(totalRow, 5, 1, 4); // E bis H
  totalRange.setFontWeight('bold');
  totalRange.setBorder(true, true, true, true, false, false);
  totalRange.setBackground('#F0F0F0');
  
  // Rechtsbündige Ausrichtung für Gesamtsumme
  sheet.getRange(totalRow, 5, 1, 2).setHorizontalAlignment('right'); // E:F
  sheet.getRange(totalRow, 7, 1, 2).setHorizontalAlignment('right'); // G:H
}

/**
 * Rechnung laden
 */
function loadInvoice(invoiceNumber) {
  const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
  const sheetName = `Rechnung-${invoiceNumber}`;
  const sheet = spreadsheet.getSheetByName(sheetName);
  
  if (sheet) {
    spreadsheet.setActiveSheet(sheet);
    return `Rechnung ${invoiceNumber} wurde geladen.`;
  } else {
    return `Rechnung ${invoiceNumber} wurde nicht gefunden.`;
  }
}
